from DrissionPage import ChromiumOptions, ChromiumPage
import os
import sys
import random
import time
import logging
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 加载环境变量
load_dotenv()

# 用户代理列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
]

def get_user_agent():
    """获取随机用户代理"""
    # 如果设置了自定义用户代理，则使用自定义的
    custom_agent = os.getenv("CUSTOM_USER_AGENT")
    if custom_agent:
        return custom_agent
    # 否则随机选择一个用户代理
    return random.choice(USER_AGENTS)

def init_browser():
    """初始化并打开浏览器"""
    try:
        logging.info("正在初始化浏览器...")
        
        # 获取Chrome选项
        co = ChromiumOptions()
        
        # 设置用户代理
        user_agent = get_user_agent()
        co.set_user_agent(user_agent)
        logging.info(f"使用用户代理: {user_agent}")
        
        # 设置各种浏览器参数
        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")

        # 启用扩展程序的关键配置
        # 移除自动化标识，让浏览器表现得更像正常浏览器
        co.set_argument("--disable-blink-features=AutomationControlled")

        # 移除可能阻止扩展程序的标识
        co.set_argument("--disable-web-security")
        co.set_argument("--disable-features=VizDisplayCompositor")

        # 启用扩展程序的多个参数
        co.set_argument("--enable-extensions")
        co.set_argument("--disable-extensions-except")  # 不禁用任何扩展
        co.set_argument("--disable-default-apps")  # 禁用默认应用但保留扩展

        # 加载自动绑卡插件
        plugin_path = os.path.join(os.getcwd(), "自动绑卡插件")
        logging.info(f"当前工作目录: {os.getcwd()}")
        logging.info(f"插件路径: {plugin_path}")
        logging.info(f"插件目录是否存在: {os.path.exists(plugin_path)}")

        if os.path.exists(plugin_path):
            # 检查manifest.json是否存在
            manifest_path = os.path.join(plugin_path, "manifest.json")
            if os.path.exists(manifest_path):
                co.set_argument(f"--load-extension={plugin_path}")
                logging.info(f"✅ 成功加载自动绑卡插件: {plugin_path}")
            else:
                logging.error(f"❌ 插件目录存在但缺少manifest.json: {manifest_path}")
        else:
            logging.warning(f"❌ 未找到自动绑卡插件目录: {plugin_path}")
            # 列出当前目录的内容
            try:
                files = os.listdir(os.getcwd())
                logging.info(f"当前目录内容: {files}")
            except Exception as e:
                logging.error(f"无法列出目录内容: {e}")

        # 确保扩展程序可以正常运行
        co.set_argument("--allow-running-insecure-content")
        co.set_argument("--disable-component-extensions-with-background-pages=false")

        # 使用系统用户数据路径以保持扩展程序状态
        use_system_profile = os.getenv("USE_SYSTEM_PROFILE", "True").lower() == "true"
        if use_system_profile:
            co.use_system_user_path(True)
            logging.info("使用系统用户数据路径，扩展程序将保持已安装状态")
        else:
            logging.info("使用临时用户数据路径")

        # 设置扩展程序相关的首选项
        co.set_pref("extensions.ui.developer_mode", True)  # 启用开发者模式
        co.set_pref("profile.default_content_setting_values.notifications", 1)  # 允许通知

        # 自动选择端口
        co.auto_port()
        
        # 设置无头模式 - 默认有头模式以便查看扩展程序
        headless = os.getenv("BROWSER_HEADLESS", "False").lower() == "true"
        co.headless(headless)
        if headless:
            logging.info("启用无头模式")
        else:
            logging.info("启用有头模式")
        
        # 针对Mac系统进行特殊设置
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")
            logging.info("检测到Mac系统，添加特殊参数")
        
        # 创建浏览器实例 - 使用ChromiumPage而不是Chromium
        browser = ChromiumPage(co)
        logging.info("浏览器初始化成功!")

        # 等待一下让扩展程序加载
        time.sleep(2)

        # 检查扩展程序是否加载成功
        try:
            # 尝试访问chrome://extensions/页面来验证扩展程序
            logging.info("检查扩展程序加载状态...")
        except Exception as e:
            logging.warning(f"无法检查扩展程序状态: {e}")

        return browser
        
    except Exception as e:
        logging.error(f"浏览器初始化失败: {str(e)}")
        raise

def open_url(browser, url):
    """打开指定URL"""
    try:
        logging.info(f"正在打开URL: {url}")
        browser.get(url)
        logging.info(f"成功打开URL: {url}")
        
        # 获取当前页面标题
        title = browser.title
        logging.info(f"页面标题: {title}")
        
        return True
    except Exception as e:
        logging.error(f"打开URL失败: {str(e)}")
        return False

def quit_browser(browser):
    """安全关闭浏览器"""
    if browser:
        try:
            logging.info("正在关闭浏览器...")
            browser.quit()
            logging.info("浏览器已关闭")
        except Exception as e:
            logging.error(f"关闭浏览器失败: {str(e)}")

def main():
    """主函数"""
    try:
        # 初始化浏览器
        browser = init_browser()
        
        # 打开网页
        url = os.getenv("TARGET_URL", "https://www.google.com")
        open_url(browser, url)
        
        # 等待一段时间（可以从环境变量中获取，默认30秒）
        wait_time = int(os.getenv("WAIT_TIME", "30"))
        logging.info(f"浏览器将保持打开状态 {wait_time} 秒...")
        
        # 倒计时
        for i in range(wait_time, 0, -1):
            if i % 5 == 0:  # 每5秒打印一次
                logging.info(f"剩余时间: {i} 秒")
            time.sleep(1)
        
    except KeyboardInterrupt:
        logging.info("检测到键盘中断，正在退出...")
    except Exception as e:
        logging.error(f"运行过程中发生错误: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
    finally:
        # 关闭浏览器
        if 'browser' in locals():
            quit_browser(browser)
        logging.info("程序已退出")

if __name__ == "__main__":
    main() 