<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-generator {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            border-radius: 8px;
            text-align: center;
        }
        .icon-16 { width: 16px; height: 16px; font-size: 8px; }
        .icon-48 { width: 48px; height: 48px; font-size: 20px; }
        .icon-128 { width: 128px; height: 128px; font-size: 48px; }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <h1>Stripe Auto Fill 插件图标生成器</h1>
    
    <div class="icon-generator">
        <h2>图标预览</h2>
        <div class="icon-preview">
            <div class="icon icon-16" id="icon16">💳</div>
            <div class="icon icon-48" id="icon48">💳</div>
            <div class="icon icon-128" id="icon128">💳</div>
        </div>
        
        <button onclick="downloadIcon(16)">下载 16x16 图标</button>
        <button onclick="downloadIcon(48)">下载 48x48 图标</button>
        <button onclick="downloadIcon(128)">下载 128x128 图标</button>
        <button onclick="downloadAllIcons()">下载所有图标</button>
    </div>
    
    <div class="instructions">
        <h3>使用说明：</h3>
        <ol>
            <li>点击上方按钮下载对应尺寸的图标文件</li>
            <li>将下载的图标文件重命名为：
                <ul>
                    <li>icon16.png (16x16)</li>
                    <li>icon48.png (48x48)</li>
                    <li>icon128.png (128x128)</li>
                </ul>
            </li>
            <li>将图标文件放在插件文件夹中</li>
            <li>按照 README.md 中的说明安装插件</li>
        </ol>
        
        <p><strong>注意：</strong>如果您有更好的图标设计，可以替换这些默认图标。建议使用信用卡、支付或自动化相关的图标设计。</p>
    </div>

    <script>
        function createIcon(size, text = '💳') {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制背景
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 绘制圆角
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();
            
            // 重置合成模式
            ctx.globalCompositeOperation = 'source-over';
            
            // 绘制图标文字
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, size / 2, size / 2);
            
            return canvas;
        }
        
        function downloadIcon(size) {
            const canvas = createIcon(size);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadAllIcons() {
            [16, 48, 128].forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }
        
        // 如果浏览器不支持 roundRect，添加 polyfill
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
