<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stripe Auto Fill</title>
    <style>
        body {
            width: 300px;
            padding: 20px;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        
        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .card-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .card-info h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .info-label {
            opacity: 0.8;
        }
        
        .info-value {
            font-weight: 500;
        }
        
        .actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .btn {
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn.primary {
            background: rgba(80, 227, 194, 0.8);
        }
        
        .btn.primary:hover {
            background: rgba(80, 227, 194, 0.9);
        }
        
        .status {
            text-align: center;
            margin-top: 15px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Stripe Auto Fill</h1>
        <p>自动填写信用卡信息</p>
    </div>
    
    <div class="card-info">
        <h3>预设卡信息</h3>
        <div class="info-item">
            <span class="info-label">卡号:</span>
            <span class="info-value">4937 **** **** 3546</span>
        </div>
        <div class="info-item">
            <span class="info-label">有效期:</span>
            <span class="info-value">06/28</span>
        </div>
        <div class="info-item">
            <span class="info-label">姓名:</span>
            <span class="info-value">Canestro</span>
        </div>
        <div class="info-item">
            <span class="info-label">国家:</span>
            <span class="info-value">Canada</span>
        </div>
    </div>
    
    <div class="actions">
        <button id="fillBtn" class="btn primary">自动填写表单</button>
        <button id="submitBtn" class="btn">自动提交</button>
        <button id="openOptionsBtn" class="btn">打开设置</button>
    </div>
    
    <div class="status" id="status">
        请在Stripe支付页面使用此插件
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
