// Popup 脚本
document.addEventListener('DOMContentLoaded', function() {
    const fillBtn = document.getElementById('fillBtn');
    const submitBtn = document.getElementById('submitBtn');
    const openOptionsBtn = document.getElementById('openOptionsBtn');
    const status = document.getElementById('status');

    // 检查当前标签页是否为支持的网站
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const currentUrl = tabs[0].url;
        const supportedUrls = [
            'checkout.stripe.com',
            'buy.stripe.com',
            '/c/pay/'
        ];
        
        const isSupported = supportedUrls.some(url => currentUrl.includes(url));
        
        if (isSupported) {
            status.textContent = '已检测到Stripe支付页面';
            status.style.background = 'rgba(80, 227, 194, 0.3)';
        } else {
            status.textContent = '请导航到Stripe支付页面';
            status.style.background = 'rgba(255, 107, 107, 0.3)';
            fillBtn.disabled = true;
            submitBtn.disabled = true;
        }
    });

    // 自动填写按钮
    fillBtn.addEventListener('click', function() {
        status.textContent = '正在填写表单...';
        
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'autoFill'}, function(response) {
                if (chrome.runtime.lastError) {
                    status.textContent = '填写失败，请刷新页面重试';
                    status.style.background = 'rgba(255, 107, 107, 0.3)';
                } else {
                    status.textContent = '表单填写完成';
                    status.style.background = 'rgba(80, 227, 194, 0.3)';
                }
            });
        });
    });

    // 自动提交按钮
    submitBtn.addEventListener('click', function() {
        status.textContent = '正在提交表单...';
        
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'autoSubmit'}, function(response) {
                if (chrome.runtime.lastError) {
                    status.textContent = '提交失败';
                    status.style.background = 'rgba(255, 107, 107, 0.3)';
                } else {
                    status.textContent = '表单已提交，验证码检测已启动';
                    status.style.background = 'rgba(80, 227, 194, 0.3)';
                }
            });
        });
    });

    // 打开设置按钮
    openOptionsBtn.addEventListener('click', function() {
        chrome.tabs.create({url: 'options.html'});
    });
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    const status = document.getElementById('status');
    
    if (request.action === 'updateStatus') {
        status.textContent = request.message;
        if (request.type === 'success') {
            status.style.background = 'rgba(80, 227, 194, 0.3)';
        } else if (request.type === 'error') {
            status.style.background = 'rgba(255, 107, 107, 0.3)';
        } else {
            status.style.background = 'rgba(255, 255, 255, 0.1)';
        }
    }
});
